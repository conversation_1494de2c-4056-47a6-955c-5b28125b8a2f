"use client";

import { useState, useEffect, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { useEditListMutation } from "@/lib/queries";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ListColorPicker } from "@/components/ui/list-color-picker";
import { List } from "@/lib/db";
import { ChevronLeft, Search } from "lucide-react";
import { SPACE_ICONS, renderSpaceIcon } from "@/lib/space-icons";

interface EditListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onListEdited: (updatedList?: List) => void;
  list: List | null;
}

export function EditListDialog({
  open,
  onOpenChange,
  onListEdited,
  list,
}: EditListDialogProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState<string | null>(null);
  const [selectedIcon, setSelectedIcon] = useState<string>("clipboard");
  const [iconSearch, setIconSearch] = useState("");

  // Refs for auto-save debouncing
  const nameTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const descriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // TanStack Query mutation
  const editListMutation = useEditListMutation(user?.id || "");

  // Reset form when dialog opens/closes or list changes
  useEffect(() => {
    if (open && list) {
      setName(list.name);
      setDescription(list.description || "");
      setColor(list.color || null);
      setSelectedIcon(list.icon || "clipboard");
      setIconSearch("");
    } else if (!open) {
      setName("");
      setDescription("");
      setColor(null);
      setSelectedIcon("clipboard");
      setIconSearch("");
    }
  }, [open, list]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (nameTimeoutRef.current) clearTimeout(nameTimeoutRef.current);
      if (descriptionTimeoutRef.current) clearTimeout(descriptionTimeoutRef.current);
    };
  }, []);

  // Auto-save functions with optimistic updates (matching edit task pattern)
  const autoSaveName = async (newName: string) => {
    if (!user || !list || newName === list.name || !newName.trim()) return;

    try {
      const result = await editListMutation.mutateAsync({
        listId: list.id,
        data: { name: newName.trim() }
      });
      if (result) {
        onListEdited(result);
      }
    } catch (error) {
      console.error("Error saving list name:", error);
      setName(list.name); // Reset on error
    }
  };

  const autoSaveDescription = async (newDescription: string) => {
    if (!user || !list || newDescription === (list.description || "")) return;

    try {
      const result = await editListMutation.mutateAsync({
        listId: list.id,
        data: { description: newDescription.trim() || null }
      });
      if (result) {
        onListEdited(result);
      }
    } catch (error) {
      console.error("Error saving list description:", error);
      setDescription(list.description || ""); // Reset on error
    }
  };

  const autoSaveColor = async (newColor: string | null) => {
    if (!user || !list || newColor === list.color) return;

    try {
      const result = await editListMutation.mutateAsync({
        listId: list.id,
        data: { color: newColor }
      });
      if (result) {
        onListEdited(result);
      }
    } catch (error) {
      console.error("Error saving list color:", error);
      setColor(list.color || null); // Reset on error
    }
  };

  const autoSaveIcon = async (newIcon: string) => {
    if (!user || !list || newIcon === list.icon) return;

    try {
      const result = await editListMutation.mutateAsync({
        listId: list.id,
        data: { icon: newIcon }
      });
      if (result) {
        onListEdited(result);
      }
    } catch (error) {
      console.error("Error saving list icon:", error);
      setSelectedIcon(list.icon || "clipboard"); // Reset on error
    }
  };

  // Debounced handlers for auto-save
  const handleNameChange = (newName: string) => {
    setName(newName);

    // Clear existing timeout
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }

    // Set new timeout for auto-save
    nameTimeoutRef.current = setTimeout(() => {
      autoSaveName(newName);
    }, 500); // 500ms debounce
  };

  const handleDescriptionChange = (newDescription: string) => {
    setDescription(newDescription);

    // Clear existing timeout
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
    }

    // Set new timeout for auto-save
    descriptionTimeoutRef.current = setTimeout(() => {
      autoSaveDescription(newDescription);
    }, 500); // 500ms debounce
  };

  const handleColorChange = (newColor: string | null) => {
    setColor(newColor);

    // Auto-save color immediately (no debounce needed for color picker)
    autoSaveColor(newColor);
  };

  const handleClose = () => {
    // Clear any pending timeouts
    if (nameTimeoutRef.current) clearTimeout(nameTimeoutRef.current);
    if (descriptionTimeoutRef.current) clearTimeout(descriptionTimeoutRef.current);

    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Edit List</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="grid gap-4 px-4 pb-4 md:px-0">
          <div className="grid gap-2">
            <Input
              id="name"
              type="search"
              value={name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="List name"
              autoComplete="off"
              spellCheck="false"
              maxLength={120}
            />
          </div>

          <div className="grid gap-2">
            <RichTextEditor
              id="description"
              value={description}
              onChange={handleDescriptionChange}
              placeholder="Add details about this list (optional)"
              maxLength={5000}
            />
          </div>

          <div className="grid gap-2">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium">Color:</span>
              <ListColorPicker
                value={color}
                onChange={handleColorChange}
              />
            </div>
          </div>

          {/* Icon Selection - mirrors space selector, 3 rows max (h-36) */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Icon</span>
              <div className="text-2xl">{renderSpaceIcon(selectedIcon, "h-6 w-6")}</div>
            </div>

            {/* Icon Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search icons..."
                value={iconSearch}
                onChange={(e) => setIconSearch(e.target.value)}
                className="pl-10"
                autoComplete="off"
                spellCheck="false"
              />
            </div>

            {/* Icon Grid */}
            <div className="h-36 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
              <div className="grid grid-cols-8 gap-2">
                {(iconSearch ? SPACE_ICONS.filter(icon => icon.toLowerCase().includes(iconSearch.toLowerCase())) : SPACE_ICONS).map((icon) => (
                  <button
                    key={icon}
                    type="button"
                    onClick={() => { setSelectedIcon(icon); autoSaveIcon(icon); }}
                    className={`h-10 w-10 rounded-lg border transition-colors flex items-center justify-center ${
                      selectedIcon === icon
                        ? "bg-primary/10 border-primary/20"
                        : "bg-card border-border hover:bg-muted/50"
                    }`}
                  >
                    {renderSpaceIcon(icon, "h-5 w-5")}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
