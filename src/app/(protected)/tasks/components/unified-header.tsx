"use client";

import { useState, useEffect, useRef } from "react";
import { Navigation } from "@/components/navigation";
import { ListNavigation } from "./list-navigation";
import { SkeletonUnifiedHeader } from "@/components/ui/skeleton";
import { List, Tag } from "@/lib/db";
import { TagFilter } from "@/lib/types";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSidebar } from "@/contexts/sidebar-context";

interface UnifiedHeaderProps {
  lists: List[];
  currentListId: string | null;
  taskCounts: Record<string, number>;
  onListSelect: (list: List) => void;
  onListsReorder: (reorderedLists: List[]) => void;
  onAddListClick: () => void;
  // Space props
  currentSpace?: { id: string; name: string; icon?: string | null } | null;
  onSpaceClick?: () => void;
  isSpaceLoading?: boolean;
  // Inline editing props
  isInlineEditEnabled?: boolean;
  onInlineEditToggle?: () => void;
  // Undo/Redo props
  canUndo: boolean;
  canRedo: boolean;
  undo: () => Promise<void>;
  redo: () => Promise<void>;
  getLastAction: () => any;
  getLastRedoAction: () => any;
  // Loading props
  isLoading?: boolean;
  showSkeleton?: boolean;
  // Tags dropdown props
  availableTags?: Tag[];
  onTagSelect?: (tag: TagFilter) => void;
  onTagCreate?: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags?: (searchTerm: string) => Promise<Tag[]>;
  activeTag?: TagFilter | null;
  onClearTagFilter?: () => void;
}

export function UnifiedHeader({
  lists,
  currentListId,
  taskCounts,
  onListSelect,
  onListsReorder,
  onAddListClick,
  currentSpace,
  onSpaceClick,
  isSpaceLoading = false,
  isInlineEditEnabled,
  onInlineEditToggle,
  canUndo,
  canRedo,
  undo,
  redo,
  getLastAction,
  getLastRedoAction,
  isLoading = false,
  showSkeleton = false,
  availableTags = [],
  onTagSelect,
  onTagCreate,
  onSearchTags,
  activeTag = null,
  onClearTagFilter,
}: UnifiedHeaderProps) {
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(96); // Default total height
  const headerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Measure header height on mount and resize
  useEffect(() => {
    const measureHeaderHeight = () => {
      if (headerRef.current) {
        const height = headerRef.current.offsetHeight;
        setHeaderHeight(height);
      }
    };

    measureHeaderHeight();
    window.addEventListener('resize', measureHeaderHeight);

    return () => {
      window.removeEventListener('resize', measureHeaderHeight);
    };
  }, []);

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          const scrollDelta = currentScrollY - lastScrollY;

          // Enable on both mobile and desktop
          // (Removed mobile-only restriction)

          // Determine scroll direction with threshold to prevent jitter
          const scrollThreshold = 8;
          const minScrollY = 100; // Minimum scroll before hiding header

          if (Math.abs(scrollDelta) > scrollThreshold) {
            if (scrollDelta > 0 && currentScrollY > minScrollY) {
              // Scrolling down and past initial threshold
              setIsScrollingDown(true);
            } else if (scrollDelta < 0 || currentScrollY <= 50) {
              // Scrolling up or near top
              setIsScrollingDown(false);
            }
          }

          setLastScrollY(currentScrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add scroll listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [lastScrollY, isScrollingDown]);

  // Calculate 50% of header height for partial hiding (hide top nav, keep list nav)
  const halfHeaderHeight = Math.round(headerHeight / 2);

  // Show skeleton loading state
  if (showSkeleton) {
    return <SkeletonUnifiedHeader />;
  }

  return (
    <div
      ref={headerRef}
      className={`sticky top-0 z-50 bg-background ${
        isDesktop ? 'w-full' : 'w-screen'
      }`}
      style={{
        willChange: 'transform',
        transform: isScrollingDown ? `translateY(-${halfHeaderHeight}px)` : 'translateY(0)',
        transition: 'transform 350ms cubic-bezier(0.4, 0, 0.2, 1)',
        ...(!isDesktop ? {
          marginLeft: 'calc(-50vw + 50%)',
          marginRight: 'calc(-50vw + 50%)',
        } : {}),
      }}
    >
      {/* Top Navigation Header - only show on mobile */}
      {!isDesktop && (
        <div className="relative">
          <Navigation
            isLoading={isLoading}
            currentSpace={currentSpace}
            onSpaceClick={onSpaceClick}
            isSpaceLoading={isSpaceLoading}
            useContainer={false}
          />
        </div>
      )}

      {/* List Navigation Header - Full width on desktop, constrained on mobile */}
      <div className="bg-background border-b border-border/50">
        {isDesktop ? (
          // Desktop: Full width list navigation
          <ListNavigation
            lists={lists}
            currentListId={currentListId}
            taskCounts={taskCounts}
            onListSelect={onListSelect}
            onListsReorder={onListsReorder}
            onAddListClick={onAddListClick}
            useContainer={false}
            isInlineEditEnabled={isInlineEditEnabled}
            onInlineEditToggle={onInlineEditToggle}
            canUndo={canUndo}
            canRedo={canRedo}
            undo={undo}
            redo={redo}
            getLastAction={getLastAction}
            getLastRedoAction={getLastRedoAction}
            availableTags={availableTags}
            onTagSelect={onTagSelect}
            onTagCreate={onTagCreate}
            onSearchTags={onSearchTags}
            activeTag={activeTag}
            onClearTagFilter={onClearTagFilter}
          />
        ) : (
          // Mobile: Constrained list navigation
          <ListNavigation
            lists={lists}
            currentListId={currentListId}
            taskCounts={taskCounts}
            onListSelect={onListSelect}
            onListsReorder={onListsReorder}
            onAddListClick={onAddListClick}
            useContainer={false}
            isInlineEditEnabled={isInlineEditEnabled}
            onInlineEditToggle={onInlineEditToggle}
            canUndo={canUndo}
            canRedo={canRedo}
            undo={undo}
            redo={redo}
            getLastAction={getLastAction}
            getLastRedoAction={getLastRedoAction}
            availableTags={availableTags}
            onTagSelect={onTagSelect}
            onTagCreate={onTagCreate}
            onSearchTags={onSearchTags}
            activeTag={activeTag}
            onClearTagFilter={onClearTagFilter}
          />
        )}
      </div>
    </div>
  );
}
