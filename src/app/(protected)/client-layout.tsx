"use client";

import { usePathname } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { BottomNavigation } from "@/components/bottom-navigation";
import { FloatingMascot } from "@/components/floating-mascot";
import { Sidebar } from "@/components/sidebar";
import { ListColorProvider } from "@/contexts/list-color-context";
import { PrimaryColorProvider } from "@/contexts/primary-color-context";
import { TagFilterProvider } from "@/contexts/tag-filter-context";
import { SidebarProvider, useSidebar } from "@/contexts/sidebar-context";
import { SpaceProvider, useSpace } from "@/contexts/space-context";
import { SpaceNavigationModal } from "@/components/spaces/space-navigation-modal";
import { CreateSpaceModal } from "@/components/spaces/create-space-modal";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { useAppInitialization } from "@/hooks/use-app-initialization";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

interface ClientLayoutProps {
  children: React.ReactNode;
  mascotPreference: "golden" | "black";
}

// Inner component that uses sidebar context
function ClientLayoutInner({
  children,
  mascotPreference
}: ClientLayoutProps) {
  const pathname = usePathname();
  const isTasksPage = pathname === "/tasks";
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed, toggleCollapse } = useSidebar();
  const {
    currentSpace,
    handleSpaceClick,
    isSpaceLoading,
    isSpaceNavigationOpen,
    setIsSpaceNavigationOpen,
    isCreateSpaceOpen,
    setIsCreateSpaceOpen,
    handleSpaceSelect,
    handleCreateSpaceClick,
    handleSpaceCreated,
    handleSpaceUpdated
  } = useSpace();

  // Initialize app with critical data preloading as early as possible
  useAppInitialization();

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar */}
      {isDesktop && (
        <Sidebar
          isCollapsed={isCollapsed}
          onToggleCollapse={toggleCollapse}
          currentSpace={currentSpace}
          onSpaceClick={handleSpaceClick}
          isSpaceLoading={isSpaceLoading}
        />
      )}

      {/* Main Content Area */}
      <div className={cn(
        "flex min-h-screen flex-col flex-1 transition-all duration-300",
        isDesktop && "ml-56",
        isDesktop && isCollapsed && "ml-16"
      )}>
        {/* Mobile Navigation - only show on mobile and when not on tasks page */}
        {!isDesktop && !isTasksPage && <Navigation />}

        <main className="flex-1 pb-16 md:pb-0">
          <div className={cn(
            // Only apply container constraint for non-tasks pages on desktop
            // Tasks page handles its own container constraints
            !isTasksPage && "container-max-width"
          )}>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        {!isDesktop && <BottomNavigation />}

        <FloatingMascot
          defaultMascot={mascotPreference}
        />

        {/* Space Modals */}
        <SpaceNavigationModal
          open={isSpaceNavigationOpen}
          onOpenChange={setIsSpaceNavigationOpen}
          currentSpaceId={currentSpace?.id}
          onSpaceSelect={handleSpaceSelect}
          onCreateSpaceClick={handleCreateSpaceClick}
          onSpaceUpdated={handleSpaceUpdated}
        />

        <CreateSpaceModal
          open={isCreateSpaceOpen}
          onOpenChange={setIsCreateSpaceOpen}
          onSpaceCreated={handleSpaceCreated}
        />
      </div>
    </div>
  );
}

export function ClientLayout({
  children,
  mascotPreference
}: ClientLayoutProps) {
  return (
    <ErrorBoundary>
      <PrimaryColorProvider>
        <ListColorProvider>
          <TagFilterProvider>
            <SidebarProvider>
              <SpaceProvider>
                <ClientLayoutInner
                  mascotPreference={mascotPreference}
                >
                  {children}
                </ClientLayoutInner>
              </SpaceProvider>
            </SidebarProvider>
          </TagFilterProvider>
        </ListColorProvider>
      </PrimaryColorProvider>
    </ErrorBoundary>
  );
}
