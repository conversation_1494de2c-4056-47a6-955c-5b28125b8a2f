"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { Search, LayoutDashboard, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { UserMenu } from "@/components/user-menu";
import { useListColor } from "@/contexts/list-color-context";
import { renderSpaceIcon } from "@/lib/space-icons";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";

// Custom N Icon component for Tasks
const NIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="Tasks"
    width={24}
    height={24}
    className={`${className} object-contain`}
    style={style}
  />
);

const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/tasks",
    label: "Tasks",
    icon: NIcon,
  },
  {
    href: "/calendar",
    label: "Calendar",
    icon: Calendar,
  },
];

interface SidebarProps {
  currentSpace?: { id: string; name: string; icon?: string | null } | null;
  onSpaceClick?: () => void;
  isSpaceLoading?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function Sidebar({
  currentSpace = null,
  onSpaceClick,
  isSpaceLoading = false,
  isCollapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  const [currentPath, setCurrentPath] = useState("");
  const pathname = usePathname();
  const { currentListColor } = useListColor();

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Get logo styles based on current list color
  const getLogoStyles = () => {
    if (!currentListColor) {
      // Plain white for colorless lists with subtle glow
      return {
        backgroundColor: '#ffffff',
        boxShadow: '0 0 8px rgba(255, 255, 255, 0.3), 0 0 16px rgba(255, 255, 255, 0.1)',
      };
    }

    // Apply solid color background based on current list color with subtle glow
    return {
      backgroundColor: currentListColor,
      boxShadow: `0 0 8px ${currentListColor}40, 0 0 16px ${currentListColor}20`,
    };
  };

  // Get grey filter for inactive N icon to match other inactive tab icons
  const getInactiveNIconFilter = () => {
    // Convert white N icon to muted grey color similar to other inactive icons
    return "brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%)";
  };

  return (
    <aside
      className={cn(
        "fixed left-0 top-0 z-40 h-full bg-background border-r border-border/50 transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-56"
      )}
    >
      <div className="flex flex-col h-full">
        {/* Header with logo - clickable to toggle */}
        <div className="flex items-center justify-center p-4 border-b border-border/50">
          <button
            type="button"
            onClick={onToggleCollapse}
            className={`relative transition-all duration-300 cursor-pointer ${
              isCollapsed ? 'w-10 h-10' : 'w-32 h-12'
            }`}
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              // N Icon when collapsed
              <div
                className="w-full h-full"
                style={{
                  ...getLogoStyles(),
                  WebkitMask: 'url(/NeoTask_Icon_N.webp) no-repeat center/contain',
                  mask: 'url(/NeoTask_Icon_N.webp) no-repeat center/contain',
                }}
              />
            ) : (
              // Full logo when expanded
              <div
                className="w-full h-full"
                style={{
                  ...getLogoStyles(),
                  WebkitMask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
                  mask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
                }}
              />
            )}
            <span className="sr-only">Toggle sidebar</span>
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {routes.map((route) => {
            const Icon = route.icon;
            const isActive = currentPath === route.href;
            const isTasksTab = route.href === "/tasks";

            return (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors hover:bg-muted/50",
                  isActive ? "bg-muted text-foreground" : "text-muted-foreground hover:text-foreground",
                  isCollapsed && "justify-center px-2"
                )}
                title={isCollapsed ? route.label : undefined}
              >
                {isTasksTab ? (
                  <Icon
                    className="h-5 w-5 flex-shrink-0"
                    style={!isActive ? {
                      filter: getInactiveNIconFilter()
                    } : undefined}
                  />
                ) : (
                  <Icon className="h-5 w-5 flex-shrink-0" />
                )}
                {!isCollapsed && <span className="font-medium">{route.label}</span>}
              </Link>
            );
          })}
        </nav>

        {/* Search Section */}
        <div className="p-4 border-t border-border/50">
          {!isCollapsed ? (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                className="pl-9"
              />
            </div>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className="w-full h-10"
              title="Search"
            >
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
          )}
        </div>

        {/* Space Section */}
        {currentSpace && (
          <div className="px-4 py-2 border-t border-border/50">
            <Button
              variant="ghost"
              onClick={onSpaceClick}
              className={cn(
                "w-full justify-start gap-3 space-name-glass rounded-lg p-3 h-auto",
                isCollapsed && "justify-center px-2"
              )}
              disabled={isSpaceLoading}
              title={isCollapsed ? currentSpace.name : undefined}
            >
              <div className="flex-shrink-0">
                {renderSpaceIcon(currentSpace.icon, "h-5 w-5")}
              </div>
              {!isCollapsed && (
                <span className="text-sm font-medium truncate flex-1 text-left">
                  {currentSpace.name}
                </span>
              )}
              {isSpaceLoading && !isCollapsed && (
                <LoadingSpinner size="sm" />
              )}
            </Button>
          </div>
        )}

        {/* User Menu */}
        <div className="p-4 border-t border-border/50">
          <div className={cn("flex", isCollapsed ? "justify-center" : "justify-end")}>
            <UserMenu />
          </div>
        </div>
      </div>
    </aside>
  );
}
