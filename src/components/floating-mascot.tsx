"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { UserSettings } from "@/lib/db";
import { cn } from "@/lib/utils";

interface FloatingMascotProps {
  defaultMascot?: "golden" | "black";
}

export function FloatingMascot({ defaultMascot = "golden" }: FloatingMascotProps) {
  const [mascot, setMascot] = useState<"golden" | "black">(defaultMascot);
  const [isLoading, setIsLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [currentPath, setCurrentPath] = useState("");
  const pathname = usePathname();

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Only show on protected pages
  const shouldShow = ["/dashboard", "/tasks", "/calendar"].some(path =>
    currentPath.startsWith(path)
  );

  // Update mascot when defaultMascot prop changes
  useEffect(() => {
    setMascot(defaultMascot);
  }, [defaultMascot]);

  // Fetch settings from API on initial load
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/settings");
        if (response.ok) {
          const settings = await response.json() as UserSettings;
          if (settings.mascot) {
            setMascot(settings.mascot as "golden" | "black");
          }
        }
      } catch (error) {
        console.error("Error fetching mascot settings:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  if (!shouldShow || isLoading) {
    return null;
  }

  return (
    <div
      className={cn(
        "fixed bottom-20 z-40 transition-all duration-300 cursor-pointer",
        "md:bottom-4",
        // Position relative to container boundaries
        "right-4", // Mobile: 16px from right edge
        "md:right-[max(1rem,calc((100vw-896px)/2+1rem))]", // Desktop: 16px from container right edge
        isHovered ? "scale-110" : "scale-100"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative w-16 h-16">
        <Image
          src={`/sprites/${mascot}.webp`}
          alt={`${mascot} mascot`}
          fill
          sizes="64px"
          className="object-contain"
          priority
        />
      </div>
    </div>
  );
}
